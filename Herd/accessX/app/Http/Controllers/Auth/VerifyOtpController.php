<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Notifications\SendOtpNotification;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;

class VerifyOtpController extends Controller
{
    /**
     * Show the OTP verification form.
     */
    public function show(Request $request): View|RedirectResponse
    {
        // If user is already authenticated and OTP verified, redirect to dashboard
        if (Auth::check() && Auth::user()->is_otp_verified) {
            return redirect()->intended(route('dashboard', absolute: false));
        }

        // If user ID is not in session, redirect to login
        if (!$request->session()->has('otp_verification_user_id')) {
            return redirect()->route('login');
        }

        // Get the user
        $user = User::find($request->session()->get('otp_verification_user_id'));
        
        // If user not found, redirect to login
        if (!$user) {
            $request->session()->forget('otp_verification_user_id');
            return redirect()->route('login');
        }

        return view('auth.verify-otp');
    }

    /**
     * Verify the OTP code.
     */
    public function verify(Request $request): RedirectResponse
    {
        $request->validate([
            'otp' => ['required', 'string', 'size:6'],
        ]);

        // Get user from session
        if (!$request->session()->has('otp_verification_user_id')) {
            return redirect()->route('login');
        }

        $user = User::find($request->session()->get('otp_verification_user_id'));
        
        if (!$user) {
            $request->session()->forget('otp_verification_user_id');
            return redirect()->route('login');
        }

        // Check if OTP matches and is not expired
        if ($user->otp === $request->otp && 
            $user->otp_expires_at && 
            $user->otp_expires_at->isFuture()) {
            
            // Mark OTP as verified
            $user->update([
                'is_otp_verified' => true,
                'otp' => null,
                'otp_expires_at' => null,
            ]);

            // Log the user in
            Auth::login($user);
            
            // Clear the OTP verification session
            $request->session()->forget('otp_verification_user_id');
            
            // Get the intended URL
            $intended = $request->session()->pull('url.intended', route('dashboard', absolute: false));
            
            // Regenerate session to prevent session fixation
            $request->session()->regenerate();

            return redirect()->intended($intended);
        }

        return back()->withErrors([
            'otp' => 'The provided OTP is invalid or has expired.',
        ]);
    }

    /**
     * Resend OTP to the user.
     */
    public function resend(Request $request): RedirectResponse
    {
        Log::info('Resend OTP requested', [
            'session_data' => $request->session()->all(),
            'has_otp_verification_user_id' => $request->session()->has('otp_verification_user_id'),
        ]);

        if (!$request->session()->has('otp_verification_user_id')) {
            Log::warning('No otp_verification_user_id in session');
            return redirect()->route('login');
        }

        $userId = $request->session()->get('otp_verification_user_id');
        Log::info('Found user ID in session', ['user_id' => $userId]);
        
        $user = User::find($userId);
        
        if (!$user) {
            Log::error('User not found in database', ['user_id' => $userId]);
            $request->session()->forget('otp_verification_user_id');
            return redirect()->route('login');
        }
        
        Log::info('Found user', ['user_id' => $user->id, 'email' => $user->email]);
        
        // Generate new OTP
        $otp = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
        $expiresAt = now()->addMinutes(10); // OTP expires in 10 minutes
        
        $user->update([
            'otp' => $otp,
            'otp_expires_at' => $expiresAt,
            'is_otp_verified' => false,
        ]);

        try {
            Log::info('Sending OTP notification', [
                'user_id' => $user->id,
                'email' => $user->email,
                'otp' => $otp
            ]);
            
            // Send OTP via email (synchronously)
            $notification = new SendOtpNotification($otp);
            $notification->onConnection('sync');
            $user->notify($notification);
            
            Log::info("OTP resent to {$user->email}", [
                'user_id' => $user->id,
                'otp_length' => strlen($otp),
                'notification_sent' => true
            ]);
            
            // For debugging: Log the OTP to the log file
            Log::info("DEBUG - Resent OTP for {$user->email}: {$otp}");
            
            return back()->with('status', 'A new OTP has been sent to your email. The OTP has also been logged for debugging.');
        } catch (\Exception $e) {
            Log::error("Failed to resend OTP email: " . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            
            // If email fails, still log the OTP and continue
            Log::info("OTP for {$user->email}: {$otp}");
            
            return back()->with('error', 'Failed to resend OTP. Please try again.');
        }
    }
}
