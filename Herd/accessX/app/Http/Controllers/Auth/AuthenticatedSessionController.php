<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\User;
use App\Notifications\SendOtpNotification;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): View
    {
        return view('auth.login');
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        Log::info('Login attempt started', ['email' => $request->email]);
        
        $request->authenticate();

        $user = $request->user();
        Log::info('User authenticated', ['user_id' => $user->id, 'email' => $user->email]);
        
        // Generate OTP
        $otp = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
        $expiresAt = now()->addMinutes(10); // OTP expires in 10 minutes
        
        Log::info('Generated OTP', ['otp' => $otp, 'expires_at' => $expiresAt]);
        
        // Save OTP to user
        $user->update([
            'otp' => $otp,
            'otp_expires_at' => $expiresAt,
            'is_otp_verified' => false,
        ]);

        Log::info('OTP saved to user', ['user_id' => $user->id]);

        // Store the intended URL before logging out
        $intended = $request->session()->pull('url.intended', route('dashboard', absolute: false));
        Log::info('Stored intended URL', ['url' => $intended]);
        
        // Log the user out to prevent access until OTP is verified
        Auth::guard('web')->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        Log::info('User logged out for OTP verification');

        // Store user ID and intended URL in session for OTP verification
        $request->session()->put([
            'otp_verification_user_id' => $user->id,
            'url.intended' => $intended,
        ]);

        Log::info('Session data stored for OTP verification', [
            'session_data' => [
                'otp_verification_user_id' => $user->id,
                'intended_url' => $intended
            ]
        ]);

        try {
            Log::info('Attempting to send OTP notification', [
                'user_id' => $user->id,
                'email' => $user->email,
                'notification_class' => SendOtpNotification::class
            ]);
            
            // Send OTP via email (synchronously)
            $notification = new SendOtpNotification($otp);
            $notification->onConnection('sync');
            $user->notify($notification);
            
            Log::info("OTP sent to {$user->email}", [
                'user_id' => $user->id,
                'otp_length' => strlen($otp),
                'notification_sent' => true
            ]);
            
            // For debugging: Log the OTP to the log file
            Log::info("DEBUG - OTP for {$user->email}: {$otp}");
            
            // Redirect to OTP verification page with success message
            return redirect()->route('otp.verify')
                ->with('status', 'We have sent an OTP to your email address.');
                
        } catch (\Exception $e) {
            Log::error("Failed to send OTP email: " . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
                'user_id' => $user->id,
                'email' => $user->email
            ]);
            
            // If email fails, still log the OTP and continue
            Log::info("OTP for {$user->email}: {$otp}");
            
            return redirect()->route('otp.verify')
                ->with('error', 'Failed to send OTP email. The OTP has been logged. Please contact support.');
        }
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }
}
