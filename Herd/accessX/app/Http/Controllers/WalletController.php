<?php

namespace App\Http\Controllers;

use App\Models\Wallet;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class WalletController extends Controller
{
    /**
     * Verify bank account details
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyAccount(Request $request)
    {
        $request->validate([
            'bank_code' => 'required|string',
            'account_number' => 'required|string|min:10|max:20|regex:/^[0-9]+$/',
        ]);

        $bankCode = $request->bank_code;
        $accountNumber = $request->account_number;
        
        // Bank code mapping for Paystack (Nigerian banks)
        $bankCodeMapping = [
            'access_bank' => '044',
            'first_bank' => '011',
            'zenith_bank' => '057',
            'uba' => '033',
            'gtb' => '058',
            'wema_bank' => '035',
            'polaris_bank' => '076',
            'fidelity_bank' => '070',
            'stanbic_ibtc_bank' => '221',
            'unity_bank' => '215',
            'fcmb' => '214',
            'ecobank' => '050',
            'heritage_bank' => '030',
            'keystone_bank' => '082',
            'stanbic_ibtc_bt' => '221',
            'sterling_bank' => '232',
            'union_bank' => '032',
            'jaiz_bank' => '301',
            'fidelity_bank' => '070',
            'fidelity_bank_plc' => '070',
            'providus_bank' => '101',
            'suntrust_bank' => '100',
            'rubies_bank' => '125',
            'kuda_bank' => '50211',
            'parallex_bank' => '104',
            'premium_trust_bank' => '105',
            'globus_bank' => '00103',
            'titan_trust_bank' => '102',
            'taaj_bank' => '090395'
        ];
        
        // Get the numeric bank code for Paystack
        $paystackBankCode = $bankCodeMapping[$bankCode] ?? $bankCode;

        try {
            $client = new \GuzzleHttp\Client();
            
            $response = $client->request('GET', 'https://api.paystack.co/bank/resolve', [
                'query' => [
                    'account_number' => $accountNumber,
                    'bank_code' => $paystackBankCode
                ],
                'headers' => [
                    'Authorization' => 'Bearer ' . env('PAYSTACK_SECRET_KEY'),
                    'Cache-Control' => 'no-cache',
                ],
                'http_errors' => false
            ]);
            
            $responseData = json_decode($response->getBody()->getContents(), true);
            
            if ($response->getStatusCode() === 200 && $responseData['status'] === true) {
                // Get the bank name from Paystack's list of banks
                $bankResponse = $client->request('GET', 'https://api.paystack.co/bank', [
                    'query' => [
                        'pay_with_bank' => true
                    ],
                    'headers' => [
                        'Authorization' => 'Bearer ' . env('PAYSTACK_SECRET_KEY')
                    ]
                ]);
                
                $banksData = json_decode($bankResponse->getBody()->getContents(), true);
                $bankName = 'Bank';
                
                if (isset($banksData['data'])) {
                    foreach ($banksData['data'] as $bank) {
                        if ($bank['code'] === $paystackBankCode) {
                            $bankName = $bank['name'];
                            break;
                        }
                    }
                }
                
                return response()->json([
                    'success' => true,
                    'account_name' => $responseData['data']['account_name'],
                    'bank_name' => $bankName,
                    'account_number' => $accountNumber,
                    'bank_code' => $bankCode
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $responseData['message'] ?? 'Failed to verify account. Please check the details and try again.'
                ], 400);
            }
            
        } catch (\Exception $e) {
            \Log::error('Account verification failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while verifying the account. Please try again later.'
            ], 500);
        }
    }
    
    public function index()
    {
        $wallet = Auth::user()->wallet;
        if (!$wallet) {
            $wallet = Wallet::create(['user_id' => Auth::id()]);
        }
        $transactions = $wallet->transactions()->latest()->paginate(10);
        $totalCredit = $wallet->transactions()->where('type', 'deposit')->sum('amount');
        $totalDebit = $wallet->transactions()->where('type', 'withdrawal')->sum('amount');
        return view('dashboard', compact('wallet', 'transactions', 'totalCredit', 'totalDebit'));
    }

    public function deposit(Request $request)
    {
        $request->validate([
            'amount' => ['required', 'numeric', 'min:0.01'],
            'description' => 'nullable|string|max:255'
        ]);

        try {
            $wallet = Auth::user()->wallet;
            $wallet->deposit($request->amount, $request->description);
            return back()->with('success', 'Deposit successful!');
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function updateBalance(Request $request)
    {
        $request->validate([
            'amount' => ['required', 'numeric', 'min:0.01']
        ]);

        try {
            $wallet = Auth::user()->wallet;
            $wallet->increment('balance', $request->amount);
            
            // Record the transaction
            $wallet->transactions()->create([
                'amount' => $request->amount,
                'type' => 'deposit',
                'description' => 'Crypto mining reward',
                'status' => 'completed'
            ]);

            return response()->json([
                'success' => true,
                'new_balance' => $wallet->balance
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    //public function withdraw(Request $request)
    //{
       // $request->validate([
        //    'amount' => ['required', 'numeric', 'min:0.01', 'max:' . Auth::user()->wallet->balance],
        //    'wallet_address' => ['required', 'string', 'min:26', 'max:35'],
        //    'description' => 'nullable|string|max:255'
       // ]);

       // try {
        //    $wallet = Auth::user()->wallet;
        //    $wallet->withdraw($request->amount, $request->description);
        //    return back()->with('success', 'Withdrawal successful! Funds will be sent to ' . $request->wallet_address);
       // } catch (\Exception $e) {
        //    return back()->with('error', $e->getMessage());
       // }
   // }



   public function withdraw(Request $request)
{
    $request->validate([
        'amount' => ['required', 'numeric', 'min:0.01', 'max:' . Auth::user()->wallet->balance],
        'wallet_address' => ['required', 'string', 'min:26', 'max:35'],
        'description' => 'nullable|string|max:255'
    ]);

    // Simulate network issue instead of processing withdrawal
    return back()->with('error', 'Network error: Unable to process withdrawal at this time. Please try again later.');
    
    // Original code commented out
    /*
    try {
        $wallet = Auth::user()->wallet;
        $wallet->withdraw($request->amount, $request->description);
        return back()->with('success', 'Withdrawal successful! Funds will be sent to ' . $request->wallet_address);
    } catch (\Exception $e) {
        return back()->with('error', $e->getMessage());
    }
    */
}

/**
 * Handle bank transfer request
 *
 * @param  \Illuminate\Http\Request  $request
 * @return \Illuminate\Http\JsonResponse
 */
public function transfer(Request $request)
{
    try {
        // Validate request data
        $validated = $request->validate([
            'amount' => ['required', 'numeric', 'min:1', 'regex:/^\d+(\.\d{1,2})?$/'],
            'bank_name' => ['required', 'string', 'max:255'],
            'account_number' => ['required', 'string', 'min:10', 'max:20', 'regex:/^[0-9]+$/'],
            'account_name' => ['required', 'string', 'min:3', 'max:255'],
            'description' => ['nullable', 'string', 'max:500']
        ], [
            'amount.regex' => 'The amount must be a valid monetary value with up to 2 decimal places.',
            'account_number.regex' => 'The account number must contain only numbers.',
            'account_name.regex' => 'The account name contains invalid characters.',
        ]);

        // Get the authenticated user's wallet
        $user = Auth::user();
        $wallet = $user->wallet;
        
        if (!$wallet) {
            return response()->json([
                'success' => false,
                'message' => 'Wallet not found. Please contact support.'
            ], 400);
        }
        
        // Calculate fee and total amount
        $amount = (float) $request->amount;
        $fee = min(10, max(1, $amount * 0.01)); // 1% fee, min $1, max $10
        $totalAmount = $amount + $fee;
        
        // Check if user has sufficient balance including fee
        if ($wallet->balance < $totalAmount) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient balance. You need $' . number_format($totalAmount, 2) . 
                             ' (including $' . number_format($fee, 2) . ' fee) but only have $' . 
                             number_format($wallet->balance, 2) . ' available.'
            ], 400);
        }
        
        // Start database transaction
        DB::beginTransaction();
        
        try {
            // Generate a unique reference
            $reference = 'TRF-' . strtoupper(Str::random(8)) . '-' . time();
            
            // Deduct amount + fee from wallet
            $wallet->decrement('balance', $totalAmount);
            
            // Record the transaction
            $transaction = $wallet->transactions()->create([
                'amount' => -$amount, // Negative for debits
                'fee' => $fee,
                'type' => 'bank_transfer',
                'status' => 'completed',
                'description' => $request->description ?? 'Bank transfer to ' . $request->bank_name,
                'reference' => $reference,
                'metadata' => [
                    'bank_name' => $request->bank_name,
                    'account_number' => $request->account_number,
                    'account_name' => $request->account_name,
                    'fee' => $fee,
                    'initiated_at' => now()->toDateTimeString(),
                    'completed_at' => now()->toDateTimeString()
                ]
            ]);
            
            // Commit the transaction
            DB::commit();
            
            // Return success response with updated balance
            return response()->json([
                'success' => true,
                'message' => 'Transfer successful!',
                'new_balance' => $wallet->fresh()->balance,
                'transaction_reference' => $reference
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
        
    } catch (\Illuminate\Validation\ValidationException $e) {
        // Handle validation errors
        return response()->json([
            'success' => false,
            'message' => 'Validation failed',
            'errors' => $e->errors()
        ], 422);
        
    } catch (\Exception $e) {
        // Log the error
        Log::error('Bank transfer failed: ' . $e->getMessage(), [
            'user_id' => Auth::id(),
            'exception' => $e
        ]);
        
        return response()->json([
            'success' => false,
            'message' => 'An error occurred while processing your transfer. Please try again or contact support if the problem persists.'
        ], 500);
    }
}
}