<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class SendOtpNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The OTP code.
     *
     * @var string
     */
    public $otp;

    /**
     * Create a new notification instance.
     */
    public function __construct(string $otp)
    {
        $this->otp = $otp;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        Log::info('Preparing OTP email', [
            'notifiable_id' => $notifiable->id,
            'email' => $notifiable->email,
            'otp' => $this->otp
        ]);

        try {
            $mail = (new MailMessage)
                        ->subject('Your One-Time Password (OTP)')
                        ->line('Your OTP code is: ' . $this->otp)
                        ->line('This code will expire in 10 minutes.')
                        ->line('If you did not request this OTP, please ignore this email.');

            Log::info('OTP email prepared successfully', [
                'notifiable_id' => $notifiable->id,
                'email' => $notifiable->email
            ]);

            return $mail;
        } catch (\Exception $e) {
            Log::error('Failed to prepare OTP email', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'otp' => $this->otp,
        ];
    }
}
