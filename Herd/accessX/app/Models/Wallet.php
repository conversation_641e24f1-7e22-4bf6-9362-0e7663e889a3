<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Wallet extends Model
{
    protected $fillable = ['user_id', 'balance'];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    public function deposit(float $amount, string $description = null): void
    {
        $this->transactions()->create([
            'amount' => $amount,
            'type' => 'deposit',
            'description' => $description,
        ]);

        $this->increment('balance', $amount);
    }

    public function withdraw(float $amount, string $description = null): void
    {
        if ($this->balance < $amount) {
            throw new \Exception('Insufficient funds');
        }

        $this->transactions()->create([
            'amount' => $amount,
            'type' => 'withdrawal',
            'description' => $description,
        ]);

        $this->decrement('balance', $amount);
    }
}