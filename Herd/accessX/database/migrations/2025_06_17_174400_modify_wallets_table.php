<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('wallets', function (Blueprint $table) {
            // Change the balance column to have a default value of 0
            $table->decimal('balance', 15, 2)->default(0)->change();
        });
    }

    public function down(): void
    {
        Schema::table('wallets', function (Blueprint $table) {
            // Revert back to not having a default value
            $table->decimal('balance', 15, 2)->default(null)->change();
        });
    }
};
