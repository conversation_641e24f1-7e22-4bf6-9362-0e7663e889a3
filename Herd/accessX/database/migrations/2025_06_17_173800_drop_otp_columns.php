<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Check if columns exist before trying to drop them
        if (Schema::hasColumn('users', 'otp')) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn('otp');
            });
        }
        
        if (Schema::hasColumn('users', 'otp_expires_at')) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn('otp_expires_at');
            });
        }
        
        if (Schema::hasColumn('users', 'is_otp_verified')) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn('is_otp_verified');
            });
        }
    }

    public function down(): void
    {
        // This migration only drops columns, so no need to implement down()
    }
};
