<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone')->nullable()->after('email');
            $table->text('address')->nullable()->after('phone');
            $table->string('city')->nullable()->after('address');
            $table->string('state')->nullable()->after('city');
            $table->string('country')->nullable()->after('state');
            $table->string('postal_code', 20)->nullable()->after('country');
            $table->date('date_of_birth')->nullable()->after('postal_code');
            $table->text('bio')->nullable()->after('date_of_birth');
            $table->string('profile_photo_path')->nullable()->after('bio');
            $table->decimal('account_balance', 15, 2)->default(0)->after('profile_photo_path');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'phone',
                'address',
                'city',
                'state',
                'country',
                'postal_code',
                'date_of_birth',
                'bio',
                'profile_photo_path',
                'account_balance'
            ]);
        });
    }
};
