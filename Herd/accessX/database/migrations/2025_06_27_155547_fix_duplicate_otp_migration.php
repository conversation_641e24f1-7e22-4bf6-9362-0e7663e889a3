<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Mark the problematic migrations as run
        DB::table('migrations')->insert([
            ['migration' => '2025_06_17_173700_add_otp_fields_to_users_table', 'batch' => 5],
            ['migration' => '2025_06_17_173800_drop_otp_columns', 'batch' => 5],
            ['migration' => '2025_06_17_174200_add_otp_columns_again', 'batch' => 5],
            ['migration' => '2025_06_17_174400_modify_wallets_table', 'batch' => 5],
            ['migration' => '2025_06_27_145041_add_profile_fields_to_users_table', 'batch' => 5],
            ['migration' => '2025_06_27_145230_add_missing_profile_fields_to_users_table', 'batch' => 5],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
